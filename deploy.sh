#!/bin/bash

set -e  # Exit on any error

echo "🚀 Starting deployment of Channel Forwarder Bot..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

print_info "Running as root user"

# Update system packages
print_info "Updating system packages..."
apt update && apt upgrade -y
print_status "System packages updated"

# Install required packages
print_info "Installing required packages..."
apt install -y python3 python3-pip python3-venv nginx certbot python3-certbot-nginx ufw git curl wget
print_status "Required packages installed"

# Create log directory
print_info "Creating log directory..."
mkdir -p /var/log/channel-forwarder
chown root:root /var/log/channel-forwarder
chmod 755 /var/log/channel-forwarder
print_status "Log directory created"

# Make production script executable
print_info "Making production script executable..."
chmod +x /root/apps/channel-forwarder/start_production.py
print_status "Production script made executable"

# Install Python dependencies
print_info "Installing Python dependencies..."
cd /root/apps/channel-forwarder
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
deactivate
print_status "Python dependencies installed"

# Install systemd service
print_info "Installing systemd service..."
cp /root/apps/channel-forwarder/channel-forwarder.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable channel-forwarder
print_status "Systemd service installed and enabled"

# Configure nginx
print_info "Configuring nginx..."
cp /root/apps/channel-forwarder/nginx-channel-forwarder.conf /etc/nginx/sites-available/channel-forwarder
ln -sf /etc/nginx/sites-available/channel-forwarder /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t
print_status "Nginx configured"

# Configure firewall
print_info "Configuring firewall..."
ufw --force enable
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 80
ufw allow 443
print_status "Firewall configured"

# Start nginx
print_info "Starting nginx..."
systemctl enable nginx
systemctl restart nginx
print_status "Nginx started"

print_info "Deployment completed successfully!"
print_warning "Next steps:"
echo "1. Run: sudo certbot --nginx -d channel.demo-projects.uz"
echo "2. Run: sudo systemctl start channel-forwarder"
echo "3. Check status: sudo systemctl status channel-forwarder"
echo "4. View logs: sudo journalctl -u channel-forwarder -f"
echo "5. Access web panel: https://channel.demo-projects.uz"
echo ""
print_info "SSL certificate setup command:"
echo "sudo certbot --nginx -d channel.demo-projects.uz --email <EMAIL> --agree-tos --no-eff-email"
