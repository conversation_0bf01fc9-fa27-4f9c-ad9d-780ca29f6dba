#!/usr/bin/env python3
"""
Channel Forwarder Bot - Production startup script
"""
import sys
import os
import logging
from main import ChannelForwarderUserBot

# Set up logging for production
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/channel-forwarder/bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def check_config():
    """Check configuration"""
    from config import API_ID, API_HASH, ADMIN_USER_ID

    if not API_ID or API_ID == 0:
        logger.error("API_ID is not configured properly in .env file!")
        return False

    if not API_HASH or API_HASH == 'your_api_hash_here':
        logger.error("API_HASH is not configured properly in .env file!")
        return False

    if not ADMIN_USER_ID or ADMIN_USER_ID == 0:
        logger.error("ADMIN_USER_ID is not configured properly in .env file!")
        return False

    return True

def main():
    """Main function"""
    logger.info("🤖 Channel Forwarder User Bot starting in production mode...")

    # Check configuration
    if not check_config():
        sys.exit(1)

    try:
        # Create and start the user bot
        bot = ChannelForwarderUserBot()
        logger.info("✅ User bot started successfully!")
        logger.info("🌐 Web admin panel: http://localhost:8009")
        logger.info("📱 Send /start command to yourself")
        
        bot.run()

    except KeyboardInterrupt:
        logger.info("⏹️ User bot stopped")
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
