from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import <PERSON>gin<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
import logging
import hashlib
from database import Database
from config import WEB_HOST, WEB_PORT, SECRET_KEY

app = Flask(__name__)
app.secret_key = SECRET_KEY

# Flask-Login sozlamalari
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Admin panelga kirish uchun login qiling!'
login_manager.login_message_category = 'info'

# Admin ma'lumotlari
ADMIN_USERNAME = "muhammadali"
ADMIN_PASSWORD = "2v2y82quy"

class User(UserMixin):
    def __init__(self, username):
        self.id = username
        self.username = username

@login_manager.user_loader
def load_user(user_id):
    if user_id == ADMIN_USERNAME:
        return User(user_id)
    return None

db = Database()

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login sahifasi"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            user = User(username)
            login_user(user)
            flash('Muvaffaqiyatli login qildingiz!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Noto\'g\'ri login yoki parol!', 'error')

    return render_template('login.html')


@app.route('/logout')
@login_required
def logout():
    """Logout"""
    logout_user()
    flash('Muvaffaqiyatli logout qildingiz!', 'info')
    return redirect(url_for('login'))


@app.route('/')
@login_required
def index():
    """Asosiy sahifa"""
    stats = db.get_stats()
    source_channels = db.get_source_channels()
    target_channels = db.get_target_channels()
    mappings = db.get_all_channel_mappings()
    message_settings = db.get_all_message_settings()

    return render_template('index.html',
                         stats=stats,
                         source_channels=source_channels,
                         target_channels=target_channels,
                         mappings=mappings,
                         message_settings=message_settings,
                         current_user=current_user)

@app.route('/add_source', methods=['POST'])
@login_required
def add_source():
    """Source kanal qo'shish"""
    channel_id = request.form.get('channel_id')
    channel_name = request.form.get('channel_name')

    if not channel_id:
        flash('Kanal ID kiritilishi shart!', 'error')
        return redirect(url_for('index'))

    if db.add_source_channel(channel_id, channel_name):
        flash(f'Source kanal muvaffaqiyatli qo\'shildi: {channel_id}', 'success')
    else:
        flash('Kanalni qo\'shishda xatolik yuz berdi!', 'error')

    return redirect(url_for('index'))

@app.route('/add_target', methods=['POST'])
@login_required
def add_target():
    """Target kanal qo'shish"""
    channel_id = request.form.get('channel_id')
    channel_name = request.form.get('channel_name')

    if not channel_id:
        flash('Kanal ID kiritilishi shart!', 'error')
        return redirect(url_for('index'))

    if db.add_target_channel(channel_id, channel_name):
        flash(f'Target kanal muvaffaqiyatli qo\'shildi: {channel_id}', 'success')
    else:
        flash('Kanalni qo\'shishda xatolik yuz berdi!', 'error')

    return redirect(url_for('index'))

@app.route('/remove_source/<channel_id>')
def remove_source(channel_id):
    """Source kanalni o'chirish"""
    if db.remove_source_channel(channel_id):
        flash(f'Source kanal o\'chirildi: {channel_id}', 'success')
    else:
        flash('Kanalni o\'chirishda xatolik yuz berdi!', 'error')

    return redirect(url_for('index'))

@app.route('/remove_target/<channel_id>')
def remove_target(channel_id):
    """Target kanalni o'chirish"""
    if db.remove_target_channel(channel_id):
        flash(f'Target kanal o\'chirildi: {channel_id}', 'success')
    else:
        flash('Kanalni o\'chirishda xatolik yuz berdi!', 'error')

    return redirect(url_for('index'))

@app.route('/api/stats')
def api_stats():
    """API orqali statistika"""
    stats = db.get_stats()
    return jsonify(stats)

@app.route('/api/channels')
def api_channels():
    """API orqali kanallar ro'yxati"""
    source_channels = db.get_source_channels()
    target_channels = db.get_target_channels()

    return jsonify({
        'source_channels': source_channels,
        'target_channels': target_channels
    })


@app.route('/link_channels', methods=['POST'])
@login_required
def link_channels():
    """Kanallarni bog'lash"""
    source_channel_id = request.form.get('source_channel_id')
    target_channel_id = request.form.get('target_channel_id')

    if not source_channel_id or not target_channel_id:
        flash('Source va target kanal ID lari kiritilishi shart!', 'error')
        return redirect(url_for('index'))

    # Kanal mavjudligini tekshirish
    source_channel = db.get_channel_by_id(source_channel_id, is_source=True)
    target_channel = db.get_channel_by_id(target_channel_id, is_source=False)

    if not source_channel:
        flash(f'Source kanal topilmadi: {source_channel_id}', 'error')
        return redirect(url_for('index'))

    if not target_channel:
        flash(f'Target kanal topilmadi: {target_channel_id}', 'error')
        return redirect(url_for('index'))

    if db.add_channel_mapping(source_channel_id, target_channel_id):
        source_name = source_channel[2] or source_channel_id
        target_name = target_channel[2] or target_channel_id
        flash(f'Kanallar muvaffaqiyatli bog\'landi: {source_name} → {target_name}', 'success')
    else:
        flash('Kanallarni bog\'lashda xatolik yuz berdi!', 'error')

    return redirect(url_for('index'))


@app.route('/unlink_channels/<source_id>/<target_id>')
def unlink_channels(source_id, target_id):
    """Kanallar bog'lanishini uzish"""
    if db.remove_channel_mapping(source_id, target_id):
        flash(f'Bog\'lanish uzildi: {source_id} → {target_id}', 'success')
    else:
        flash('Bog\'lanishni uzishda xatolik yuz berdi!', 'error')

    return redirect(url_for('index'))


@app.route('/api/mappings')
def api_mappings():
    """API orqali kanal bog'lanishlari"""
    mappings = db.get_all_channel_mappings()
    return jsonify({'mappings': mappings})


@app.route('/diagnostics')
@login_required
def diagnostics():
    """Diagnostika sahifasi"""
    source_channels = db.get_source_channels()
    target_channels = db.get_target_channels()

    return render_template('diagnostics.html',
                         source_channels=source_channels,
                         target_channels=target_channels,
                         current_user=current_user)


@app.route('/set_message_settings', methods=['POST'])
@login_required
def set_message_settings():
    """Message settings sozlash"""
    source_channel_id = request.form.get('source_channel_id')
    send_mode = request.form.get('send_mode')
    custom_caption = request.form.get('custom_caption')
    instagram_enabled = request.form.get('instagram_enabled') == 'on'
    instagram_caption = request.form.get('instagram_caption')

    if not source_channel_id or not send_mode:
        flash('Source kanal va yuborish rejimi kiritilishi shart!', 'error')
        return redirect(url_for('index'))

    # Custom caption ni tozalash (bo'sh bo'lsa None qilish)
    if custom_caption and custom_caption.strip():
        custom_caption = custom_caption.strip()
    else:
        custom_caption = None

    # Instagram caption ni tekshirish
    if instagram_caption and instagram_caption.strip():
        instagram_caption = instagram_caption.strip()
    else:
        instagram_caption = None

    if db.set_message_settings(source_channel_id, send_mode, custom_caption, instagram_enabled, instagram_caption):
        source_channel = db.get_channel_by_id(source_channel_id, is_source=True)
        source_name = source_channel[2] if source_channel else source_channel_id
        instagram_status = "yoqildi" if instagram_enabled else "o'chirildi"
        flash(f'Message settings sozlandi: {source_name} -> {send_mode}, Instagram: {instagram_status}', 'success')
    else:
        flash('Message settings sozlashda xatolik yuz berdi!', 'error')

    return redirect(url_for('index'))

def run_web_admin():
    """Web admin panelni ishga tushirish"""
    try:
        app.run(host=WEB_HOST, port=WEB_PORT, debug=False)
    except Exception as e:
        logging.error(f"Web admin panelni ishga tushirishda xatolik: {e}")

if __name__ == '__main__':
    run_web_admin()
