# Channel Forwarder User Bot

Bu Pyrogram user bot kanaldan kelgan photo bilan xabarlarni boshqa kanallarga avtomatik forward qiladi.

## Xususiyatlar

- ✅ Source kanaldan photo bilan xabarlarni olish
- ✅ **Media group** (bir nechta rasm birgalikda) ni to'g'ri yuborish
- ✅ **Moslashuvchan kanal bog'lanishlari** - har bir source uchun alohida target kanallar
- ✅ Target kanallarga avtomatik copy qilish (forward emas)
- ✅ Web admin panel
- ✅ Statistika va monitoring
- ✅ SQLite ma'lumotlar bazasi
- ✅ Logging va xatoliklarni kuzatish
- ✅ FloodWait xatoliklarini avtomatik boshqarish

## O'rnatish

1. **Repository ni clone qiling:**
```bash
git clone <repository_url>
cd channel-forwarder-bot
```

2. **Virtual environment yarating:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# yoki
venv\Scripts\activate  # Windows
```

3. **Kerakli kutubxonalarni o'rnating:**
```bash
pip install -r requirements.txt
```

4. **API ma'lumotlarini oling:**
   - https://my.telegram.org ga boring
   - "API development tools" bo'limiga kiring
   - API ID va API Hash ni oling

5. **Konfiguratsiya:**
   - `.env` faylini tahrirlang
   - `API_ID` ga API ID ni kiriting
   - `API_HASH` ga API Hash ni kiriting
   - `ADMIN_USER_ID` ga sizning Telegram user ID ni kiriting

## Ishlatish

1. **User botni ishga tushiring:**
```bash
python main.py
```

2. **Birinchi marta ishga tushirganda:**
   - Telefon raqamingizni kiriting
   - SMS kod ni kiriting
   - Agar 2FA yoqilgan bo'lsa, parolni kiriting

3. **Web admin panelga kiring:**
   - Brauzerda `http://localhost:5000` ga boring

4. **Kanallarni sozlang:**
   - Source kanallar - bu yerdan xabarlar olinadi
   - Target kanallar - bu yerga xabarlar forward qilinadi
   - User bot barcha kanallarga a'zo bo'lishi kerak!

## Bot Komandalar

### Asosiy komandalar:
- `/start` - Botni ishga tushirish
- `/help` - Yordam

### Kanal boshqaruvi:
- `/add_source <kanal_id> [nom]` - Source kanal qo'shish
- `/add_target <kanal_id> [nom]` - Target kanal qo'shish
- `/list_channels` - Kanallar ro'yxati

### Kanal bog'lanishlari:
- `/link_channels <source> <target>` - Source va target kanallarni bog'lash
- `/unlink_channels <source> <target>` - Bog'lanishni uzish
- `/show_mappings` - Barcha bog'lanishlarni ko'rsatish

### Monitoring:
- `/stats` - Statistika

### Misol:
```
# 1. Kanallar qo'shish
/add_source @my_source_channel Mening source kanalim
/add_target @target1 Birinchi target
/add_target @target2 Ikkinchi target

# 2. Bog'lanishlar yaratish
/link_channels @my_source_channel @target1
/link_channels @my_source_channel @target2

# 3. Bog'lanishlarni ko'rish
/show_mappings
```

## Kanal ID ni topish

### Public kanallar:
```
@channel_username
```

### Private kanallar:
```
-1001234567890
```

**Muhim:** User bot barcha kanallarga a'zo sifatida qo'shilgan bo'lishi kerak!

## Web Admin Panel

Web admin panel `http://localhost:5000` da mavjud va quyidagi imkoniyatlarni beradi:

- 📊 **Real-time statistika** - forward qilingan xabarlar soni
- ➕ **Kanallar qo'shish/o'chirish** - source va target kanallar
- 🔗 **Kanal bog'lanishlari** - har bir source uchun target kanallar tanlash
- 📋 **Kanallar ro'yxati** - barcha kanallarni ko'rish
- 🔄 **Avtomatik yangilanish** - real-time ma'lumotlar

### Web Admin Panel xususiyatlari:

1. **Dropdown tanlash** - source va target kanallarni oson tanlash
2. **Jadval ko'rinishi** - barcha bog'lanishlarni ko'rish
3. **Bir-click o'chirish** - bog'lanishlarni oson uzish
4. **Responsive dizayn** - mobil qurilmalarda ham ishlaydi

## Fayl Tuzilishi

```
channel-forwarder-bot/
├── main.py              # Asosiy bot fayli
├── config.py            # Konfiguratsiya
├── database.py          # Ma'lumotlar bazasi
├── web_admin.py         # Web admin panel
├── requirements.txt     # Python kutubxonalar
├── .env                 # Muhit o'zgaruvchilari
├── templates/
│   └── index.html       # Web admin HTML
└── README.md           # Bu fayl
```

## Xatoliklarni hal qilish

### User bot ishlamayapti:
1. API ID va API Hash to'g'ri ekanligini tekshiring
2. User bot barcha kanallarga a'zo qo'shilganligini tekshiring
3. Session fayli (.session) mavjudligini tekshiring
4. Log faylni tekshiring: `bot.log`

### Web admin ochilmayapti:
1. Port band emasligini tekshiring
2. Firewall sozlamalarini tekshiring

### Forward ishlamayapti:
1. User bot target kanallarda a'zo ekanligini tekshiring
2. Kanal ID lar to'g'ri ekanligini tekshiring
3. FloodWait xatoligi bo'lsa, biroz kuting

### USERNAME_NOT_OCCUPIED xatoligi:
1. Target kanal username noto'g'ri kiritilgan
2. Username mavjud emas yoki o'zgartirilgan
3. Private kanallar uchun raqamli ID ishlatiladi: `-1001234567890`
4. Public kanallar uchun: `@channel_username`

### Kanal bog'lanishlari ishlamayapti:
1. Web admin panelda bog'lanishlar to'g'ri yaratilganligini tekshiring
2. Source va target kanallar avval qo'shilgan bo'lishi kerak
3. `/show_mappings` komandasi bilan bog'lanishlarni tekshiring

## Litsenziya

MIT License

## Yordam

Agar savollaringiz bo'lsa, issue yarating yoki bog'laning.
