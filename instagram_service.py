import logging
import os
from instagrapi import Client as InstagramClient
from pyrogram import Client
from pyrogram.types import Message
from config import INSTAGRAM_USERNAME, INSTAGRAM_PASSWORD, SESSION_FILE_PATH
from database import Database


logger = logging.getLogger(__name__)


class InstagramService:
    def __init__(self, db: Database):
        self.db = db
        self.instagram_client = None
        self.instagram_logged_in = False

    def login_to_instagram(self):
        """Instagram hisobiga kirish, sessions.json dan foydalanib"""
        try:
            self.instagram_client = InstagramClient()
            if os.path.exists(SESSION_FILE_PATH):
                logger.info(f"Loading session from {SESSION_FILE_PATH}")
                self.instagram_client.load_settings(SESSION_FILE_PATH)
                self.instagram_client.login(INSTAGRAM_USERNAME, INSTAGRAM_PASSWORD)
                self.instagram_logged_in = True
                logger.info("Instagram hisobiga sessions.json or<PERSON>li muvaffaqiyatli kirildi")
            else:
                logger.info("sessions.json fayli topilmadi, yangi login jarayoni boshlanmoqda")
                self.instagram_client.login(INSTAGRAM_USERNAME, INSTAGRAM_PASSWORD)
                self.instagram_client.dump_settings(SESSION_FILE_PATH)
                self.instagram_logged_in = True
                logger.info(f"New session saved to {SESSION_FILE_PATH}")
        except Exception as e:
            logger.error(f"Instagram hisobiga kirishda xatolik: {e}")
            self.instagram_logged_in = False
            if os.path.exists(SESSION_FILE_PATH):
                os.remove(SESSION_FILE_PATH)
                logger.info(f"Invalid session, {SESSION_FILE_PATH} deleted. Retrying login...")
                self.login_to_instagram()

    async def download_media(self, client: Client, file_id: str, file_type: str) -> str:
        """Download media from Telegram"""
        try:
            file = await client.download_media(file_id, f"temp_{file_id}.{file_type}")
            return file
        except Exception as e:
            logger.error(f"Media yuklab olishda xatolik: {e}")
            return None

    async def post_to_instagram(self, message: Message, client: Client):
        """Post content to Instagram"""
        source_channel_id = str(message.chat.id)
        message_settings = self.db.get_message_settings(source_channel_id)

        instagram_enabled = message_settings[4] if message_settings and len(message_settings) > 4 else False
        instagram_caption = message_settings[5] if message_settings and len(message_settings) > 5 else None

        if not instagram_enabled:
            logger.info(f"Instagram posting is disabled for channel {source_channel_id}")
            return

        if not self.instagram_logged_in:
            self.login_to_instagram()
            if not self.instagram_logged_in:
                logger.error("Instagram login failed, cannot post")
                return

        try:
            final_caption = ""
            if instagram_caption:
                final_caption = instagram_caption
            elif message.caption:
                final_caption = message.caption
            elif message.media_group_id:
                media_group_messages = await client.get_media_group(
                    chat_id=source_channel_id,
                    message_id=message.id
                )
                for msg in media_group_messages:
                    if msg.caption:
                        final_caption = msg.caption
                        break

            if message.photo and not message.media_group_id:
                photo_path = await self.download_media(client, message.photo.file_id, "jpg")
                if photo_path:
                    self.instagram_client.photo_upload(
                        path=photo_path,
                        caption=final_caption
                    )
                    logger.info(f"Instagram-ga photo post qilindi: {source_channel_id}, caption: {final_caption[:50]}...")
                    os.remove(photo_path)

            elif message.media_group_id:
                media_group_messages = await client.get_media_group(
                    chat_id=source_channel_id,
                    message_id=message.id
                )
                media_paths = []
                for msg in media_group_messages:
                    if msg.photo:
                        path = await self.download_media(client, msg.photo.file_id, "jpg")
                        if path:
                            media_paths.append(path)
                    elif msg.video:
                        path = await self.download_media(client, msg.video.file_id, "mp4")
                        if path:
                            media_paths.append(path)

                if media_paths:
                    self.instagram_client.album_upload(
                        paths=media_paths,
                        caption=final_caption
                    )
                    logger.info(f"Instagram-ga media group post qilindi: {source_channel_id}, caption: {final_caption[:50]}...")
                    for path in media_paths:
                        os.remove(path)

            self.db.add_instagram_post_stat(source_channel_id, message.id, True)

        except Exception as e:
            logger.error(f"Instagram-ga post yuborishda xatolik: {e}")
            self.db.add_instagram_post_stat(source_channel_id, message.id, False)
