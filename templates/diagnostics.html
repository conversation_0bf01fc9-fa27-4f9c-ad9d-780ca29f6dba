<!DOCTYPE html>
<html lang="uz">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostika - Channel Forwarder Bot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-robot"></i> Channel Forwarder Bot
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home"></i> Bosh sahifa
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt"></i> Chiqish
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-stethoscope"></i> Diagnostika</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Xatolikni hal qilish uchun:</h6>
                            <ol>
                                <li><strong>Kanal ID ni tekshiring</strong> - To'g'ri formatda ekanligini tasdiqlang</li>
                                <li><strong>Bot ruxsatlarini tekshiring</strong> - Bot barcha kanallarda a'zo bo'lishi kerak</li>
                                <li><strong>Kanal turi</strong> - Faqat public yoki bot a'zo bo'lgan private kanallar ishlaydi</li>
                            </ol>
                        </div>

                        <!-- Source Channels -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>Source Kanallar:</h6>
                                {% if source_channels %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Kanal ID</th>
                                                    <th>Nomi</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for channel in source_channels %}
                                                    <tr>
                                                        <td><code>{{ channel[1] }}</code></td>
                                                        <td>{{ channel[2] or 'Noma\'lum' }}</td>
                                                        <td>
                                                            {% if channel[3] %}
                                                                <span class="badge bg-success">Faol</span>
                                                            {% else %}
                                                                <span class="badge bg-danger">Nofaol</span>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                {% else %}
                                    <div class="text-muted">Source kanallar yo'q</div>
                                {% endif %}
                            </div>

                            <!-- Target Channels -->
                            <div class="col-md-6">
                                <h6>Target Kanallar:</h6>
                                {% if target_channels %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Kanal ID</th>
                                                    <th>Nomi</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for channel in target_channels %}
                                                    <tr>
                                                        <td><code>{{ channel[1] }}</code></td>
                                                        <td>{{ channel[2] or 'Noma\'lum' }}</td>
                                                        <td>
                                                            {% if channel[3] %}
                                                                <span class="badge bg-success">Faol</span>
                                                            {% else %}
                                                                <span class="badge bg-danger">Nofaol</span>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                {% else %}
                                    <div class="text-muted">Target kanallar yo'q</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Common Issues -->
                        <div class="row">
                            <div class="col-12">
                                <h6>Keng tarqalgan muammolar va yechimlar:</h6>
                                <div class="accordion" id="troubleshootingAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="headingOne">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                                "Peer id invalid" xatoligi
                                            </button>
                                        </h2>
                                        <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                            <div class="accordion-body">
                                                <strong>Sabablari:</strong>
                                                <ul>
                                                    <li>Bot kanal a'zosi emas</li>
                                                    <li>Kanal ID noto'g'ri formatda</li>
                                                    <li>Kanal private va bot ruxsati yo'q</li>
                                                </ul>
                                                <strong>Yechimlar:</strong>
                                                <ul>
                                                    <li>Botni barcha kanallarga admin sifatida qo'shing</li>
                                                    <li>Kanal ID ni qayta tekshiring</li>
                                                    <li>Public kanallar uchun @username ishlatib ko'ring</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="headingTwo">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                                <i class="fas fa-ban text-danger me-2"></i>
                                                Xabarlar forward bo'lmayapti
                                            </button>
                                        </h2>
                                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                            <div class="accordion-body">
                                                <strong>Tekshirish kerak:</strong>
                                                <ul>
                                                    <li>Source va target kanallar to'g'ri bog'langanmi?</li>
                                                    <li>Message settings sozlanganmi?</li>
                                                    <li>Bot barcha kanallarda admin huquqiga egami?</li>
                                                    <li>Faqat photo va media group xabarlar forward qilinadi</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="headingThree">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                                <i class="fab fa-instagram text-primary me-2"></i>
                                                Instagram posting ishlamayapti
                                            </button>
                                        </h2>
                                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                            <div class="accordion-body">
                                                <strong>Tekshirish kerak:</strong>
                                                <ul>
                                                    <li>Instagram login ma'lumotlari to'g'rimi?</li>
                                                    <li>Instagram posting kanal uchun yoqilganmi?</li>
                                                    <li>sessions.json fayli mavjudmi?</li>
                                                    <li>Instagram hisobi bloklangan emas?</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
