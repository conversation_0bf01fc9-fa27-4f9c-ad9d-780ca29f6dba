<!DOCTYPE html>
<html lang="uz">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Channel Forwarder Bot - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .channel-card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .source-channel {
            border-left: 4px solid #007bff;
        }
        .target-channel {
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-robot"></i> Channel Forwarder Bot - Admin Panel
            </span>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user"></i> {{ current_user.username }}
                </span>
                <a class="btn btn-outline-warning btn-sm me-2" href="{{ url_for('diagnostics') }}">
                    <i class="fas fa-stethoscope"></i> Diagnostika
                </a>
                <a class="btn btn-outline-light btn-sm" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Statistika -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-paper-plane fa-2x mb-2"></i>
                    <h3>{{ stats.total_forwards or 0 }}</h3>
                    <p>Muvaffaqiyatli Forward</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h3>{{ stats.failed_forwards or 0 }}</h3>
                    <p>Muvaffaqiyatsiz Forward</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-download fa-2x mb-2"></i>
                    <h3>{{ stats.source_channels or 0 }}</h3>
                    <p>Source Kanallar</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-upload fa-2x mb-2"></i>
                    <h3>{{ stats.target_channels or 0 }}</h3>
                    <p>Target Kanallar</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Source Kanallar -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-download"></i> Source Kanallar</h5>
                    </div>
                    <div class="card-body">
                        <!-- Source kanal qo'shish formi -->
                        <form method="POST" action="{{ url_for('add_source') }}" class="mb-3">
                            <div class="row mb-2">
                                <div class="col-md-8">
                                    <label class="form-label"><strong>Kanal ID:</strong></label>
                                    <input type="text" class="form-control" name="channel_id" placeholder="@username yoki -1001234567890" required>
                                    <small class="text-muted">Masalan: @my_channel yoki -1001234567890</small>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button class="btn btn-primary w-100" type="submit">
                                        <i class="fas fa-plus"></i> Source qo'shish
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label"><strong>Kanal nomi (ixtiyoriy):</strong></label>
                                    <input type="text" class="form-control" name="channel_name" placeholder="Masalan: Mening yangiliklar kanalim">
                                </div>
                            </div>
                        </form>

                        <!-- Source kanallar ro'yxati -->
                        {% if source_channels %}
                            {% for channel in source_channels %}
                                <div class="channel-card source-channel card p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ channel[1] }}</strong>
                                            {% if channel[2] %}
                                                <br><small class="text-muted">{{ channel[2] }}</small>
                                            {% endif %}
                                        </div>
                                        <a href="{{ url_for('remove_source', channel_id=channel[1]) }}"
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('Rostdan ham o\'chirmoqchimisiz?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>Hech qanday source kanal yo'q</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Target Kanallar -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-upload"></i> Target Kanallar</h5>
                    </div>
                    <div class="card-body">
                        <!-- Target kanal qo'shish formi -->
                        <form method="POST" action="{{ url_for('add_target') }}" class="mb-3">
                            <div class="row mb-2">
                                <div class="col-md-8">
                                    <label class="form-label"><strong>Kanal ID:</strong></label>
                                    <input type="text" class="form-control" name="channel_id" placeholder="@username yoki -1001234567890" required>
                                    <small class="text-muted">Masalan: @target_channel yoki -1001234567890</small>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button class="btn btn-success w-100" type="submit">
                                        <i class="fas fa-plus"></i> Target qo'shish
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label"><strong>Kanal nomi (ixtiyoriy):</strong></label>
                                    <input type="text" class="form-control" name="channel_name" placeholder="Masalan: Mening target kanalim">
                                </div>
                            </div>
                        </form>

                        <!-- Target kanallar ro'yxati -->
                        {% if target_channels %}
                            {% for channel in target_channels %}
                                <div class="channel-card target-channel card p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ channel[1] }}</strong>
                                            {% if channel[2] %}
                                                <br><small class="text-muted">{{ channel[2] }}</small>
                                            {% endif %}
                                        </div>
                                        <a href="{{ url_for('remove_target', channel_id=channel[1]) }}"
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('Rostdan ham o\'chirmoqchimisiz?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>Hech qanday target kanal yo'q</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Settings -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-cog"></i> Message Settings</h5>
                    </div>
                    <div class="card-body">
                        <!-- Message settings formi -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <h6>Source kanal uchun yuborish rejimini sozlang:</h6>
                                <form method="POST" action="{{ url_for('set_message_settings') }}">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label"><strong>Source kanal:</strong></label>
                                            <select class="form-select" name="source_channel_id" required>
                                                <option value="">Source kanalni tanlang</option>
                                                {% for channel in source_channels %}
                                                    <option value="{{ channel[1] }}">{{ channel[2] or channel[1] }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label"><strong>Yuborish rejimi:</strong></label>
                                            <select class="form-select" name="send_mode" required>
                                                <option value="media_group_with_caption">Media Group + Asl Caption</option>
                                                <option value="media_group_only">Faqat Media Group</option>
                                                <option value="media_group_with_custom_caption">Media Group + Custom Caption</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <label class="form-label"><strong>Custom Caption (ixtiyoriy):</strong></label>
                                            <textarea class="form-control" name="custom_caption" rows="3"
                                                      placeholder="Agar 'Faqat Media Group' yoki 'Media Group + Custom Caption' tanlasangiz, bu caption qo'shiladi"></textarea>
                                            <small class="text-muted">Bu caption media group bilan birga yoki alohida yuboriladi</small>
                                        </div>
                                    </div>

                                    <!-- Instagram Settings -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="card border-warning">
                                                <div class="card-header bg-warning text-dark">
                                                    <h6 class="mb-0"><i class="fab fa-instagram"></i> Instagram Settings</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-3">
                                                        <div class="col-12">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" name="instagram_enabled" id="instagram_enabled">
                                                                <label class="form-check-label" for="instagram_enabled">
                                                                    <strong>Instagram-ga avtomatik post qilish</strong>
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">Bu kanal uchun Instagram posting yoqiladi</small>
                                                        </div>
                                                    </div>
                                                    <div class="row mb-3">
                                                        <div class="col-12">
                                                            <label class="form-label"><strong>Instagram Caption (ixtiyoriy):</strong></label>
                                                            <textarea class="form-control" name="instagram_caption" rows="3"
                                                                      placeholder="Instagram uchun maxsus caption. Bo'sh qoldirilsa, asl xabar caption ishlatiladi"></textarea>
                                                            <small class="text-muted">Bu caption faqat Instagram postlari uchun ishlatiladi</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-save"></i> Sozlamalarni saqlash
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Mavjud message settings -->
                        <h6>Mavjud sozlamalar:</h6>
                        {% if message_settings %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Source kanal</th>
                                            <th>Yuborish rejimi</th>
                                            <th>Custom Caption</th>
                                            <th>Instagram</th>
                                            <th>Instagram Caption</th>
                                            <th>Yangilangan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for setting in message_settings %}
                                            <tr>
                                                <td>
                                                    <strong>{{ setting[8] or setting[1] }}</strong>
                                                    <br><small class="text-muted">{{ setting[1] }}</small>
                                                </td>
                                                <td>
                                                    {% if setting[2] == 'media_group_with_caption' %}
                                                        <span class="badge bg-success">Media Group + Asl Caption</span>
                                                    {% elif setting[2] == 'media_group_only' %}
                                                        <span class="badge bg-warning">Faqat Media Group</span>
                                                    {% elif setting[2] == 'media_group_with_custom_caption' %}
                                                        <span class="badge bg-info">Media Group + Custom Caption</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if setting[3] %}
                                                        <small>{{ setting[3][:50] }}{% if setting[3]|length > 50 %}...{% endif %}</small>
                                                    {% else %}
                                                        <span class="text-muted">Yo'q</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if setting[4] %}
                                                        <span class="badge bg-success"><i class="fab fa-instagram"></i> Yoqilgan</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">O'chirilgan</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if setting[5] %}
                                                        <small>{{ setting[5][:50] }}{% if setting[5]|length > 50 %}...{% endif %}</small>
                                                    {% else %}
                                                        <span class="text-muted">Asl caption</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <small>{{ setting[8][:16] if setting[8] else 'Noma\'lum' }}</small>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center text-muted">
                                <i class="fas fa-cog fa-3x mb-3"></i>
                                <p>Hech qanday message settings yo'q</p>
                                <p>Yuqoridagi forma orqali source kanallar uchun sozlamalar yarating</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Kanal bog'lanishlari -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-link"></i> Kanal bog'lanishlari</h5>
                    </div>
                    <div class="card-body">
                        <!-- Bog'lanish yaratish formi -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>Yangi bog'lanish yaratish:</h6>
                                <form method="POST" action="{{ url_for('link_channels') }}">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <select class="form-select" name="source_channel_id" required>
                                                <option value="">Source kanalni tanlang</option>
                                                {% for channel in source_channels %}
                                                    <option value="{{ channel[1] }}">{{ channel[2] or channel[1] }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <span class="fw-bold">→</span>
                                        </div>
                                        <div class="col-md-5">
                                            <select class="form-select" name="target_channel_id" required>
                                                <option value="">Target kanalni tanlang</option>
                                                {% for channel in target_channels %}
                                                    <option value="{{ channel[1] }}">{{ channel[2] or channel[1] }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-link"></i> Bog'lash
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Mavjud bog'lanishlar -->
                        <h6>Mavjud bog'lanishlar:</h6>
                        {% if mappings %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Source kanal</th>
                                            <th></th>
                                            <th>Target kanal</th>
                                            <th>Yaratilgan</th>
                                            <th>Amallar</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for mapping in mappings %}
                                            <tr>
                                                <td>
                                                    <strong>{{ mapping[1] or mapping[0] }}</strong>
                                                    <br><small class="text-muted">{{ mapping[0] }}</small>
                                                </td>
                                                <td class="text-center">
                                                    <i class="fas fa-arrow-right text-primary"></i>
                                                </td>
                                                <td>
                                                    <strong>{{ mapping[3] or mapping[2] }}</strong>
                                                    <br><small class="text-muted">{{ mapping[2] }}</small>
                                                </td>
                                                <td>
                                                    <small>{{ mapping[4][:16] if mapping[4] else 'Noma\'lum' }}</small>
                                                </td>
                                                <td>
                                                    <a href="{{ url_for('unlink_channels', source_id=mapping[0], target_id=mapping[2]) }}"
                                                       class="btn btn-sm btn-outline-danger"
                                                       onclick="return confirm('Rostdan ham bog\'lanishni uzmoqchimisiz?')">
                                                        <i class="fas fa-unlink"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center text-muted">
                                <i class="fas fa-unlink fa-3x mb-3"></i>
                                <p>Hech qanday bog'lanish yo'q</p>
                                <p>Yuqoridagi forma orqali source va target kanallarni bog'lang</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Qo'shimcha ma'lumot -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-info-circle"></i> Qo'llanma</h5>
                    </div>
                    <div class="card-body">
                        <h6>Bot qanday ishlaydi:</h6>
                        <ol>
                            <li><strong>Source kanal</strong> - Bu yerdan xabarlar olinadi</li>
                            <li><strong>Target kanallar</strong> - Bu kanallarga xabarlar copy qilinadi</li>
                            <li><strong>Bog'lanishlar</strong> - Har bir source kanal uchun alohida target kanallar belgilanadi</li>
                            <li><strong>Message Settings</strong> - Har bir source kanal uchun yuborish rejimi sozlanadi</li>
                            <li>Bot faqat <strong>photo va media group</strong> xabarlarni copy qiladi</li>
                        </ol>

                        <h6>Yuborish rejimlari:</h6>
                        <ul>
                            <li><strong>Media Group + Asl Caption:</strong> Asl media group va caption bilan yuboriladi (default)</li>
                            <li><strong>Faqat Media Group:</strong> Media group caption siz yuboriladi, keyin custom caption (agar mavjud bo'lsa)</li>
                            <li><strong>Media Group + Custom Caption:</strong> Media group yuboriladi, faqat custom caption birinchi rasmda ko'rinadi (asl caption o'chiriladi)</li>
                        </ul>

                        <h6>Qadamlar:</h6>
                        <ol>
                            <li>Source va target kanallarni qo'shing</li>
                            <li>"Message Settings" bo'limida har bir source kanal uchun yuborish rejimini sozlang</li>
                            <li>"Kanal bog'lanishlari" bo'limida bog'lanishlar yarating</li>
                            <li>Misol: 3ta source → 6ta target, 4ta source → 1ta target</li>
                        </ol>

                        <h6>Kanal ID ni qanday topish mumkin:</h6>
                        <ul>
                            <li><strong>Public kanallar:</strong> <code>@channel_username</code></li>
                            <li><strong>Private kanallar:</strong> <code>-1001234567890</code> (raqamli ID)</li>
                            <li><strong>Muhim:</strong> User bot barcha kanallarga a'zo sifatida qo'shilgan bo'lishi kerak</li>
                        </ul>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-exclamation-triangle"></i> Diqqat!</h6>
                            <ul class="mb-0">
                                <li>Kanal ID larini to'g'ri kiriting</li>
                                <li>Username mavjud bo'lishi kerak</li>
                                <li>User bot kanalga a'zo bo'lishi shart</li>
                                <li>Private kanallar uchun raqamli ID ishlatiladi</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Statistikani avtomatik yangilash
        setInterval(function() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // Statistikani yangilash logikasi
                    console.log('Stats updated:', data);
                })
                .catch(error => console.error('Error:', error));
        }, 30000); // 30 soniyada bir marta
    </script>
</body>
</html>
