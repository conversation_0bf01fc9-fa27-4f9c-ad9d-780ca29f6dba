#!/bin/bash

echo "🤖 Channel Forwarder Bot - <PERSON>'r<PERSON>ish skripti"
echo "============================================="

# Python versiyasini tekshirish
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 topilmadi! Iltimos Python3 ni o'rnating."
    exit 1
fi

echo "✅ Python3 topildi: $(python3 --version)"

# Virtual environment yaratish
if [ ! -d "venv" ]; then
    echo "📦 Virtual environment yaratilmoqda..."
    python3 -m venv venv
    echo "✅ Virtual environment yaratildi"
else
    echo "✅ Virtual environment mavjud"
fi

# Virtual environment ni faollashtirish
echo "🔄 Virtual environment faollashtirilmoqda..."
source venv/bin/activate

# Kutubxonalarni o'rnatish
echo "📚 Kerakli kutubxonalar o'rnatilmoqda..."
pip install --upgrade pip
pip install -r requirements.txt

echo ""
echo "✅ O'rnatish yakunlandi!"
echo ""
echo "📝 Keyingi qadamlar:"
echo "1. .env faylini tahrirlang:"
echo "   - BOT_TOKEN ni o'zgartiring"
echo "   - ADMIN_USER_ID ni o'zgartiring"
echo ""
echo "2. Botni ishga tushiring:"
echo "   python run.py"
echo ""
echo "3. Web admin panel:"
echo "   http://localhost:5000"
echo ""
echo "🎉 Omad tilaymiz!"
