import asyncio
import logging
from pyrogram import Client, filters
from pyrogram.types import Message, InputMediaPhoto, InputMediaVideo, InputMediaDocument
from pyrogram.errors import FloodWait, RPCError
from database import Database
from config import API_ID, API_HASH, SESSION_NAME, SAVED_MESSAGES_CHAT_ID

logger = logging.getLogger(__name__)


class TelegramService:
    def __init__(self, db: Database):
        self.db = db
        self.app = None
        self.processed_media_groups = set()

    async def start_client(self):
        """Initialize and start the Telegram client"""
        try:
            self.app = Client(
                SESSION_NAME,
                api_id=API_ID,
                api_hash=API_HASH
            )
            self.app.on_message(filters.photo & filters.channel)(self.handle_channel_message)
            self.app.on_message(filters.channel)(self.handle_message_logging)
            logger.info("Telegram client ishga tushirildi...")
            await self.app.start()
            while True:
                await asyncio.sleep(3600)  # Keep the client running
        except Exception as e:
            logger.error(f"Telegram clientni ishga tushirishda xatolik: {e}")
        finally:
            if self.app:
                await self.app.stop()

    async def handle_channel_message(self, client: Client, message: Message, instagram_service=None):
        """Handle incoming channel messages and forward to target channels"""
        logger.info(f"handle_channel_message called for message: {message.id}")
        try:
            if not message.photo:
                return

            source_channel_id = str(message.chat.id)
            source_channels = self.db.get_source_channels()
            is_source_channel = any(channel[1] == source_channel_id for channel in source_channels)

            if not is_source_channel:
                return

            # Media group check
            is_media_group = message.media_group_id is not None
            if is_media_group:
                media_group_key = f"{source_channel_id}_{message.media_group_id}"
                if media_group_key in self.processed_media_groups:
                    return
                self.processed_media_groups.add(media_group_key)

            # Post to Instagram if provided
            if instagram_service:
                await instagram_service.post_to_instagram(message, client)

            # Forward to target channels
            target_channels = self.db.get_target_channels_for_source(source_channel_id)
            if not target_channels:
                return

            message_settings = self.db.get_message_settings(source_channel_id)
            send_mode = message_settings[2] if message_settings else 'media_group_with_caption'
            custom_caption = message_settings[3] if message_settings else None

            for target_channel in target_channels:
                target_channel_id = target_channel[1]
                target_channel_name = target_channel[2] or target_channel_id

                logger.info(f"Target kanal ma'lumotlari: ID={target_channel_id}, Name={target_channel_name}")

                try:
                    actual_target_id = target_channel_id
                    if target_channel_id.startswith('@'):
                        chat = await client.get_chat(target_channel_id)
                        actual_target_id = chat.id
                    elif target_channel_id.isdigit() or (target_channel_id.startswith('-') and target_channel_id[1:].isdigit()):
                        actual_target_id = int(target_channel_id)
                    else:
                        logger.error(f"Noto'g'ri kanal ID formati: {target_channel_id}")
                        self.db.add_forward_stat(source_channel_id, target_channel_id, message.id, False)
                        continue

                    if send_mode == 'media_group_with_caption':
                        if is_media_group:
                            await client.copy_media_group(
                                chat_id=actual_target_id,
                                from_chat_id=source_channel_id,
                                message_id=message.id
                            )
                        else:
                            await client.copy_message(
                                chat_id=actual_target_id,
                                from_chat_id=source_channel_id,
                                message_id=message.id
                            )

                    elif send_mode == 'media_group_only':
                        if is_media_group:
                            media_group_messages = await client.get_media_group(
                                chat_id=source_channel_id,
                                message_id=message.id
                            )
                            media_list = []
                            for msg in media_group_messages:
                                if msg.photo:
                                    media_list.append(InputMediaPhoto(media=msg.photo.file_id))
                                elif msg.video:
                                    media_list.append(InputMediaVideo(media=msg.video.file_id))
                                elif msg.document:
                                    media_list.append(InputMediaDocument(media=msg.document.file_id))
                            await client.send_media_group(
                                chat_id=actual_target_id,
                                media=media_list
                            )
                            if custom_caption:
                                await client.send_message(
                                    chat_id=actual_target_id,
                                    text=custom_caption
                                )
                        else:
                            await client.send_photo(
                                chat_id=actual_target_id,
                                photo=message.photo.file_id
                            )
                            if custom_caption:
                                await client.send_message(
                                    chat_id=actual_target_id,
                                    text=custom_caption
                                )

                    elif send_mode == 'media_group_with_custom_caption':
                        if is_media_group:
                            media_group_messages = await client.get_media_group(
                                chat_id=source_channel_id,
                                message_id=message.id
                            )
                            media_list = []
                            for i, msg in enumerate(media_group_messages):
                                if i == 0:
                                    combined_caption = custom_caption if custom_caption else ""
                                    if msg.photo:
                                        media_list.append(InputMediaPhoto(
                                            media=msg.photo.file_id,
                                            caption=combined_caption
                                        ))
                                    elif msg.video:
                                        media_list.append(InputMediaVideo(
                                            media=msg.video.file_id,
                                            caption=combined_caption
                                        ))
                                    elif msg.document:
                                        media_list.append(InputMediaDocument(
                                            media=msg.document.file_id,
                                            caption=combined_caption
                                        ))
                                else:
                                    if msg.photo:
                                        media_list.append(InputMediaPhoto(
                                            media=msg.photo.file_id,
                                            caption=msg.caption
                                        ))
                                    elif msg.video:
                                        media_list.append(InputMediaVideo(
                                            media=msg.video.file_id,
                                            caption=msg.caption
                                        ))
                                    elif msg.document:
                                        media_list.append(InputMediaDocument(
                                            media=msg.document.file_id,
                                            caption=msg.caption
                                        ))
                            await client.send_media_group(
                                chat_id=actual_target_id,
                                media=media_list
                            )
                        else:
                            combined_caption = custom_caption if custom_caption else ""
                            await client.send_photo(
                                chat_id=actual_target_id,
                                photo=message.photo.file_id,
                                caption=combined_caption
                            )

                    self.db.add_forward_stat(source_channel_id, target_channel_id, message.id, True)

                except (RPCError, FloodWait) as e:
                    if isinstance(e, FloodWait):
                        logger.warning(f"FloodWait: {e.value} soniya kutish kerak")
                        await asyncio.sleep(e.value)
                        continue
                    self.db.add_forward_stat(source_channel_id, target_channel_id, message.id, False)
                    logger.error(f"Yuborishda xatolik: {source_channel_id} -> {target_channel_id}, {e}")

                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Xabarni qayta ishlashda xatolik: {e}")

    async def handle_message_logging(self, client: Client, message: Message):
        """Log messages from source channels and forward to saved messages"""
        try:
            source_channel_id = str(message.chat.id)
            source_channels = self.db.get_source_channels()
            is_source_channel = any(channel[1] == source_channel_id for channel in source_channels)

            if not is_source_channel:
                return

            source_channel = self.db.get_channel_by_id(source_channel_id, is_source=True)
            source_channel_name = source_channel[2] if source_channel else f"Channel {source_channel_id}"

            message_type = "text"
            has_media = False
            media_type = None
            message_text = message.text or message.caption

            if message.photo:
                message_type = "photo"
                has_media = True
                media_type = "photo"
            elif message.video:
                message_type = "video"
                has_media = True
                media_type = "video"
            elif message.document:
                message_type = "document"
                has_media = True
                media_type = "document"

            user_id = message.from_user.id if message.from_user else None
            username = message.from_user.username if message.from_user else None
            media_group_id = str(message.media_group_id) if message.media_group_id else None

            log_success = self.db.add_message_log(
                source_channel_id=source_channel_id,
                source_channel_name=source_channel_name,
                message_id=message.id,
                message_type=message_type,
                message_text=message_text,
                has_media=has_media,
                media_type=media_type,
                media_group_id=media_group_id,
                user_id=user_id,
                username=username
            )

            if log_success:
                logger.info(f"Xabar log qilindi: {source_channel_name} -> {message_type} (ID: {message.id})")

            log_text = f"📨 **Yangi xabar log**\n\n"
            log_text += f"🔗 **Kanal:** {source_channel_name}\n"
            log_text += f"🆔 **Kanal ID:** `{source_channel_id}`\n"
            log_text += f"📝 **Xabar ID:** `{message.id}`\n"
            log_text += f"📋 **Turi:** {message_type}\n"

            if has_media:
                log_text += f"🎭 **Media turi:** {media_type}\n"

            if media_group_id:
                log_text += f"🗂 **Media group ID:** `{media_group_id}`\n"

            if user_id:
                log_text += f"👤 **User ID:** `{user_id}`\n"

            if username:
                log_text += f"👤 **Username:** @{username}\n"

            if message_text:
                text_preview = message_text[:200] + "..." if len(message_text) > 200 else message_text
                log_text += f"💬 **Matn:** {text_preview}\n"

            log_text += f"⏰ **Vaqt:** {message.date.strftime('%Y-%m-%d %H:%M:%S')}\n"

            await client.send_message(
                chat_id=SAVED_MESSAGES_CHAT_ID,
                text=log_text
            )

            await client.forward_messages(
                chat_id=SAVED_MESSAGES_CHAT_ID,
                from_chat_id=source_channel_id,
                message_ids=message.id
            )

            logger.info(f"Xabar saved messages ga yuborildi: {message.id}")

        except Exception as e:
            logger.error(f"Message logging da xatolik: {e}")
