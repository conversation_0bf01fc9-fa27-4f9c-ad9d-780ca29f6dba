import os
from dotenv import load_dotenv

load_dotenv()

# Pyrogram User Bot konfiguratsiyasi
API_ID = int(os.getenv('API_ID', '0'))
API_HASH = os.getenv('API_HASH')
SESSION_NAME = "telegram_session"
PHONE_NUMBER = os.getenv('PHONE_NUMBER')
ADMIN_USER_ID = int(os.getenv('ADMIN_USER_ID', '0'))

# Web admin konfiguratsiyasi
WEB_HOST = os.getenv('WEB_HOST', '127.0.0.1')
WEB_PORT = int(os.getenv('WEB_PORT', '5000'))
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')

# Ma'lumotlar bazasi
DATABASE_PATH = 'bot_database.db'

# Logging konfiguratsiyasi
LOG_LEVEL = 'INFO'
LOG_FILE = 'bot.log'

# Saved Messages konfiguratsiyasi
SAVED_MESSAGES_CHAT_ID = os.getenv('SAVED_MESSAGES_CHAT_ID', 'me')

# Instagram konfiguratsiyasi
INSTAGRAM_USERNAME = os.getenv('INSTAGRAM_USERNAME', 'milliy_fasonlar_olami')
INSTAGRAM_PASSWORD = os.getenv('INSTAGRAM_PASSWORD', '2V2Y82QUY')
SESSION_FILE_PATH = 'instagram_sessions.json'
