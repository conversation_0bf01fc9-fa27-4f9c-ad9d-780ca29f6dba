[Unit]
Description=Channel Forwarder Telegram Bot
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/apps/channel-forwarder
Environment=PATH=/root/apps/channel-forwarder/venv/bin
ExecStart=/root/apps/channel-forwarder/venv/bin/python /root/apps/channel-forwarder/run.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=channel-forwarder

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/root/apps/channel-forwarder
ReadWritePaths=/var/log/channel-forwarder

[Install]
WantedBy=multi-user.target
