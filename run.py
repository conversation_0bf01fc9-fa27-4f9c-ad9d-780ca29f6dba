import asyncio
import logging
import threading
from database import Database
from telegram_service import TelegramService
from instagram_service import InstagramService
from web_admin import run_web_admin

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("bot.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ChannelForwarderUserBot:
    def __init__(self):
        self.db = Database()
        self.telegram_service = TelegramService(self.db)
        self.instagram_service = InstagramService(self.db)

    def run(self):
        """Run the bot, starting the web admin and Telegram service"""
        try:
            web_thread = threading.Thread(target=run_web_admin, daemon=True)
            web_thread.start()
            logger.info("Web admin panel ishga tushirildi")

            # Start Telegram service with Instagram service dependency
            asyncio.run(self.telegram_service.start_client())
        except Exception as e:
            logger.error(f"Botni ishga tushirishda xatolik: {e}")


if __name__ == "__main__":
    bot = ChannelForwarderUserBot()
    bot.run()
